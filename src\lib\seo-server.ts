import { headers } from 'next/headers'

/**
 * 获取基础URL
 * 从请求头中提取主机信息，并返回以https协议为前缀的完整URL。
 * 注意：部分CDN默认不支持x-forwarded-proto，因此协议固定为https。
 * 
 * 此函数只能在服务器组件中使用
 *
 * @returns 包含协议和主机的完整URL字符串
 */
export const getBaseUrl = async (): Promise<string> => {
  const headersList = await headers()
  const host = headersList.get('x-forwarded-host') || headersList.get('host')
  return `https://${host}` // 协议指定为https (部分CDN默认不支持x-forwarded-proto)
}
