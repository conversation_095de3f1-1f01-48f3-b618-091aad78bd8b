import { Metadata } from 'next'
import { Locale, siteConfig } from '@/config/global'
import { i18nConfig } from '@/config/global'
import { LOCALES } from '@/config/global'
import { trimEnd } from 'lodash'

interface PageSEOProps {
  title: string
  description: string
  pathname: string
  locale: Locale
  availableLocales?: Locale[]
  type?: 'article' | 'website'
  image?: string
  [key: string]: any
}

export function genLocalePathname(pathname: string, locale: Locale) {
  if (!pathname.startsWith('/')) {
    pathname = `/${pathname}`
  }
  if (locale != i18nConfig.defaultLocale) {
    pathname = trimEnd(`/${locale}${pathname}`, '/')
  }
  return pathname
}

const SUPPORTED_I18N_ARTICLES = [
  '/article/privacy-policy',
  '/article/terms-of-service',
  '/article/how-do-i-save-videos-on-my-iphone-ipad-or-ipod',
]
export function getOtherLanguagePathname(pathname: string): string {
  if (pathname.startsWith('/article/') && !SUPPORTED_I18N_ARTICLES.includes(pathname)) {
    // if is not supported i18n articels, redirect to home
    return '/'
  } else if (pathname.startsWith('/articles/')) {
    // strip page number
    return pathname.split('/').slice(0, 3).join('/')
  }
  return pathname
}

export function genPageMetadata({
  title,
  description,
  pathname,
  locale,
  availableLocales,
  type,
  image,
  ...rest
}: PageSEOProps): Metadata {
  const currentPathname = genLocalePathname(pathname, locale)
  // Generate alternate links for all available languages
  const locales = availableLocales || LOCALES
  const languages: Record<string, string> = {}
  if (!availableLocales) {
    languages['x-default'] = genLocalePathname(pathname, i18nConfig.defaultLocale)
  }
  locales.forEach((locale) => {
    languages[locale] = genLocalePathname(pathname, locale)
  })

  return {
    title,
    description,
    alternates: {
      canonical: currentPathname,
      languages: languages,
    },
    openGraph: {
      title: title,
      description: description,
      url: currentPathname,
      siteName: siteConfig.name,
      images: image ? [image] : [siteConfig.ogImage],
      type: type || 'website',
    },
    twitter: {
      title: title,
      description: description,
      card: 'summary_large_image',
      images: image ? [image] : [siteConfig.ogImage],
    },
    ...rest,
  }
}