import type { MetadataRoute } from 'next'
import { LOCALES, i18nConfig } from '@/config/global'
import { getBaseUrl } from '@/lib/seo-server'
import { SITE_IDS } from '@/config/downloader'

// 按菜单栏顺序排列的前台页面路径（排除动态路由页面）
const frontendPages = [
  '/', // 首页
  '/app', // 应用下载页面
  '/downloader', // 下载器页面
]

// 支持的视频网站页面
const sitePages = SITE_IDS.map(siteId => `/${siteId}`)

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = await getBaseUrl()

  const sitemapEntries: MetadataRoute.Sitemap = []

  // 合并所有页面路径
  const allPages = [...frontendPages, ...sitePages]

  // 为每个页面生成sitemap条目
  for (const path of allPages) {
    // 为每种语言生成单独的sitemap条目
    for (const locale of LOCALES) {
      const localizedPath = locale === i18nConfig.defaultLocale
        ? path
        : `/${locale}${path === '/' ? '' : path}`

      const url = `${baseUrl}${localizedPath === '/' ? '' : localizedPath}`

      // 设置优先级：首页最高，基础功能页面其次，视频网站页面较低
      let priority = 0.6 // 视频网站页面默认优先级
      if (path === '/') {
        priority = 1.0 // 首页
      } else if (frontendPages.includes(path)) {
        priority = 0.8 // 基础功能页面
      }

      sitemapEntries.push({
        url,
        lastModified: new Date(),
        changeFrequency: 'weekly',
        priority,
      })
    }
  }

  return sitemapEntries
}