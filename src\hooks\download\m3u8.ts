import { useCallback } from 'react';
import type { DownloadItem, DownloadProgress, UseM3u8DownloadProps, LiveRecordingController } from '@/types/download';
import { DownloadStatus } from '@/types/download';
import {
  parseM3u8Content,
  parseM3u8ContentWithLiveDetection,
  downloadSegmentWithProgress,
  getSegmentsInfo,
  type SegmentInfo
} from '@/lib/download/m3u8';
import { getVideoDurationFromBlob } from '@/lib/download/video';
import { downloadFileWithHeaders } from '@/lib/download/controller';
import { HlsRecorder } from '@/lib/download/hls';

/**
 * M3U8下载处理Hook
 * 负责M3U8流媒体文件的下载逻辑
 */
export function useM3u8Download({
  state,
  updateProgress,
  setDownloadBlobUrl,
  updateDownloadDataDuration,
  saveToLocal,
  setLiveRecordingController,
  t
}: UseM3u8DownloadProps) {

  // M3U8文件下载处理
  const downloadM3u8File = useCallback(async (item: DownloadItem) => {
    try {
      // 检查是否已保存，避免重复保存
      if (state.savedFiles.has(item.requestId)) {
        updateProgress(item.requestId, {
          status: DownloadStatus.COMPLETED,
          percentage: 100
        });
        return;
      }

      // 第一步：设置请求头规则（确保M3U8和TS分片请求使用正确的Origin和Referer）
      updateProgress(item.requestId, {
        status: DownloadStatus.DOWNLOADING,
        percentage: 0
      });

      const result = await downloadFileWithHeaders(item, state.pageTaskId!);
      console.log(`M3U8请求头设置完成: ${result.data?.filename || item.filename}`);

      // 第二步：获取M3U8播放列表
      updateProgress(item.requestId, {
        status: DownloadStatus.DOWNLOADING,
        percentage: 0
      });

      const m3u8Response = await fetch(item.url, {
        headers: item.requestHeaders
          ? Object.fromEntries(
            item.requestHeaders.map((h) => [h.name, h.value])
          )
          : {}
      });

      if (!m3u8Response.ok) {
        throw new Error(`Failed to fetch M3U8: HTTP ${m3u8Response.status}`);
      }

      const m3u8Content = await m3u8Response.text();

      // 第三步：解析M3U8内容并检测直播流
      updateProgress(item.requestId, {
        status: DownloadStatus.DOWNLOADING,
        percentage: 0
      });

      const parseResult = parseM3u8ContentWithLiveDetection(m3u8Content, item.url);
      let segments = parseResult.segments;
      const liveStreamInfo = parseResult.liveStreamInfo;

      // 如果检测到直播流，更新下载项标识
      if (liveStreamInfo.isLiveStream) {
        item.isLiveStream = true;

        updateProgress(item.requestId, {
          status: DownloadStatus.LIVE_RECORDING,
          percentage: 10
        });
      }

      // 检查是否需要二次解析（主播放列表返回了子播放列表URL）
      if (
        segments.length === 1 &&
        (segments[0].includes('.m3u8') || segments[0].includes('m3u8'))
      ) {
        updateProgress(item.requestId, {
          status: DownloadStatus.DOWNLOADING,
          percentage: 0
        });

        const subResponse = await fetch(segments[0], {
          headers: item.requestHeaders
            ? Object.fromEntries(
              item.requestHeaders.map((h) => [h.name, h.value])
            )
            : {}
        });

        if (!subResponse.ok) {
          throw new Error(`Failed to fetch sub playlist: HTTP ${subResponse.status}`);
        }

        const subContent = await subResponse.text();

        segments = parseM3u8Content(subContent, segments[0]);
      }

      if (segments.length === 0) {
        throw new Error(t?.('noVideoSegmentsFound'));
      }

      // 第三步：根据是否为直播流选择不同的下载策略
      let downloadedSegments: ArrayBuffer[];

      if (item.isLiveStream) {
        // 直播流：使用HLS.js录制模式
        downloadedSegments = await downloadLiveStreamWithHls(
          item,
          updateProgress,
          setLiveRecordingController
        );
      } else {
        // 点播文件：一次性下载所有分片
        downloadedSegments = await downloadAllSegments(
          segments,
          item,
          updateProgress
        );
      }

      // 第四步：合并分片并创建Blob URL
      await mergeSegments(
        downloadedSegments,
        item,
        updateProgress,
        setDownloadBlobUrl,
        updateDownloadDataDuration,
        saveToLocal
      );

    } catch (error) {
      console.error('M3U8下载失败:', error);
      updateProgress(item.requestId, {
        status: DownloadStatus.ERROR,
        errorText: (error as Error).message,
        percentage: 0
      });
      throw error;
    }
  }, [state.savedFiles, state.pageTaskId, updateProgress, setDownloadBlobUrl, updateDownloadDataDuration, saveToLocal, setLiveRecordingController, t]);

  return {
    downloadM3u8File
  };
}

/**
 * 下载所有分片
 */
async function downloadAllSegments(
  segments: string[],
  item: DownloadItem,
  updateProgress: (requestId: string, progress: Partial<DownloadProgress[string]>) => void
): Promise<ArrayBuffer[]> {
  // 第一步：获取所有分片的大小信息
  updateProgress(item.requestId, {
    status: DownloadStatus.DOWNLOADING,
    percentage: 0
  });

  let segmentsInfo: SegmentInfo[] | null = null;
  let totalSize = 0;
  let useAdvancedProgress = false;

  try {
    segmentsInfo = await getSegmentsInfo(
      segments,
      item.requestHeaders
    );

    totalSize = segmentsInfo.reduce((sum, segment) => sum + segment.size, 0);
    useAdvancedProgress = true;
  } catch (error) {
    console.warn('获取分片大小失败，使用传统进度计算:', error);
    // 继续执行，但使用传统的进度计算方式
  }

  // 第二步：下载所有分片
  const downloadedSegments: ArrayBuffer[] = [];
  let downloadedSize = 0;
  const startTime = Date.now();
  let lastUpdateTime = startTime;

  const segmentsToDownload = useAdvancedProgress ? segmentsInfo! : segments.map(url => ({ url }));

  for (let i = 0; i < segmentsToDownload.length; i++) {
    const segmentItem = segmentsToDownload[i];
    const segmentUrl = 'url' in segmentItem ? segmentItem.url : segmentItem;

    try {
      const segmentData = await downloadSegmentWithProgress(
        segmentUrl,
        item.requestHeaders,
        (segmentDownloaded, segmentTotal) => {
          const currentTime = Date.now();

          // 限制更新频率为500ms，避免过于频繁的UI更新
          if (currentTime - lastUpdateTime < 500) return;
          lastUpdateTime = currentTime;

          const currentSegmentProgress = segmentDownloaded;
          const totalDownloaded = downloadedSize + currentSegmentProgress;

          // 计算下载速度
          const elapsedSeconds = (currentTime - startTime) / 1000;
          const speed = elapsedSeconds > 0 ? totalDownloaded / elapsedSeconds : 0;

          // 计算进度百分比
          let percentage: number;
          if (useAdvancedProgress && totalSize > 0) {
            // 基于实际大小的精确进度
            percentage = (totalDownloaded / totalSize) * 100;
          } else {
            // 基于分片数量的传统进度
            percentage = ((i + segmentDownloaded / segmentTotal) / segmentsToDownload.length) * 100;
          }

          updateProgress(item.requestId, {
            status: DownloadStatus.DOWNLOADING,
            percentage: Math.min(100, percentage),
            downloadedSize: totalDownloaded,
            totalSize: useAdvancedProgress ? totalSize : undefined,
            speed: speed
          });
        }
      );

      downloadedSegments.push(segmentData);
      downloadedSize += segmentData.byteLength;

    } catch (error) {
      console.error(`下载分片失败 (${i + 1}/${segmentsToDownload.length}):`, error);
      throw error;
    }
  }

  // 最终更新进度
  const finalTime = Date.now();
  const totalElapsedSeconds = (finalTime - startTime) / 1000;
  const averageSpeed = totalElapsedSeconds > 0 ? downloadedSize / totalElapsedSeconds : 0;

  updateProgress(item.requestId, {
    status: DownloadStatus.DOWNLOADING,
    percentage: 100,
    downloadedSize: downloadedSize,
    totalSize: useAdvancedProgress ? totalSize : downloadedSize,
    speed: averageSpeed
  });

  return downloadedSegments;
}

/**
 * 合并分片并创建Blob URL
 */
async function mergeSegments(
  downloadedSegments: ArrayBuffer[],
  item: DownloadItem,
  updateProgress: (requestId: string, progress: Partial<DownloadProgress[string]>) => void,
  setDownloadBlobUrl: (blobUrl: string | null) => void,
  updateDownloadDataDuration: (duration: string | null) => void,
  saveToLocal: (blobUrl?: string) => Promise<void>
): Promise<void> {
  // 合并分片（后台进行，不显示进度）
  const mergedBlob = new Blob(downloadedSegments, { type: 'video/mp4' });
  const finalSize = mergedBlob.size;

  // 创建 Blob URL，等待用户手动保存
  const blobUrl = URL.createObjectURL(mergedBlob);

  // 保存 Blob URL 到状态
  setDownloadBlobUrl(blobUrl);

  // 尝试获取文件时长（后台进行，不显示进度）
  const duration = await getVideoDurationFromBlob(blobUrl);
  if (duration) {
    updateDownloadDataDuration(duration);
  }

  // 下载完成，自动触发保存
  updateProgress(item.requestId, {
    status: DownloadStatus.COMPLETED,
    percentage: 100,
    downloadedSize: finalSize,
    totalSize: finalSize
  });

  // 自动触发保存
  try {
    await saveToLocal(blobUrl);
  } catch (error) {
    console.error('自动保存失败:', error);
    updateProgress(item.requestId, {
      status: DownloadStatus.ERROR,
      errorText: (error as Error).message,
      percentage: 100
    });
  }
}

/**
 * 直播流下载函数
 * 支持持续录制和AbortController中断
 */
async function downloadLiveStreamWithHls(
  item: DownloadItem,
  updateProgress: (requestId: string, progress: Partial<DownloadProgress[string]>) => void,
  setLiveRecordingController: (controller: LiveRecordingController | null) => void
): Promise<ArrayBuffer[]> {
  // 创建HLS录制器
  const hlsRecorder = new HlsRecorder(item, updateProgress);

  // 设置录制控制器
  const controller = hlsRecorder.getController();
  setLiveRecordingController(controller);
  try {
    // 使用HLS录制器开始录制
    const recordedData = await hlsRecorder.startRecording();

    // 清理录制控制器状态
    setLiveRecordingController(null);

    return recordedData;
  } catch (error) {
    // 清理录制控制器状态
    setLiveRecordingController(null);
    console.error('HLS录制失败:', error);
    throw error;
  }
}
